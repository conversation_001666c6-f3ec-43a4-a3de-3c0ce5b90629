// components/ink-input/ink-input.js - 水墨风输入框组件
Component({
  properties: {
    // 输入框值
    value: {
      type: String,
      value: ''
    },
    // 占位符
    placeholder: {
      type: String,
      value: '请输入内容'
    },
    // 输入框类型
    type: {
      type: String,
      value: 'text'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 最大长度
    maxlength: {
      type: Number,
      value: 140
    },
    // 标签文字
    label: {
      type: String,
      value: ''
    },
    // 是否必填
    required: {
      type: Boolean,
      value: false
    },
    // 错误信息
    error: {
      type: String,
      value: ''
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  data: {
    focused: false,
    currentValue: ''
  },

  observers: {
    'value': function(newVal) {
      this.setData({
        currentValue: newVal
      });
    }
  },

  lifetimes: {
    attached() {
      this.setData({
        currentValue: this.data.value
      });
    }
  },

  methods: {
    // 输入事件
    onInput(e) {
      const value = e.detail.value;
      this.setData({
        currentValue: value
      });
      
      this.triggerEvent('input', {
        value: value
      });
    },

    // 获得焦点
    onFocus(e) {
      this.setData({
        focused: true
      });
      
      this.triggerEvent('focus', e.detail);
    },

    // 失去焦点
    onBlur(e) {
      this.setData({
        focused: false
      });
      
      this.triggerEvent('blur', e.detail);
    },

    // 确认输入
    onConfirm(e) {
      this.triggerEvent('confirm', e.detail);
    },

    // 清空输入
    onClear() {
      this.setData({
        currentValue: ''
      });
      
      this.triggerEvent('input', {
        value: ''
      });
      
      this.triggerEvent('clear');
    }
  }
});
