/* components/ink-loading/ink-loading.wxss - 水墨风加载动画样式 */

.ink-loading {
  --loading-color: var(--ink-black);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: 'STSong', '华文宋体', serif;
}

/* 毛笔书写动画 */
.brush-loading {
  position: relative;
  width: 80rpx;
  height: 80rpx;
}

.brush-stroke {
  position: absolute;
  background: var(--loading-color);
  border-radius: 2rpx;
  transform-origin: bottom center;
}

.stroke-1 {
  width: 4rpx;
  height: 40rpx;
  left: 20rpx;
  top: 10rpx;
  animation: brushStroke1 2s ease-in-out infinite;
}

.stroke-2 {
  width: 6rpx;
  height: 30rpx;
  left: 35rpx;
  top: 20rpx;
  animation: brushStroke2 2s ease-in-out infinite 0.3s;
}

.stroke-3 {
  width: 4rpx;
  height: 35rpx;
  left: 50rpx;
  top: 15rpx;
  animation: brushStroke3 2s ease-in-out infinite 0.6s;
}

.brush-handle {
  position: absolute;
  width: 8rpx;
  height: 60rpx;
  background: linear-gradient(to bottom, #8B4513, #D2691E);
  border-radius: 4rpx;
  left: 36rpx;
  top: 50rpx;
  animation: brushHandle 2s ease-in-out infinite;
}

@keyframes brushStroke1 {
  0%, 100% { transform: scaleY(0); opacity: 0; }
  20%, 80% { transform: scaleY(1); opacity: 1; }
}

@keyframes brushStroke2 {
  0%, 100% { transform: scaleY(0); opacity: 0; }
  20%, 80% { transform: scaleY(1); opacity: 1; }
}

@keyframes brushStroke3 {
  0%, 100% { transform: scaleY(0); opacity: 0; }
  20%, 80% { transform: scaleY(1); opacity: 1; }
}

@keyframes brushHandle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}

/* 水墨点扩散动画 */
.dots-loading {
  position: relative;
  width: 100rpx;
  height: 100rpx;
}

.ink-dot {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background: var(--loading-color);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.dot-1 { animation: inkDot 1.5s ease-in-out infinite 0s; }
.dot-2 { animation: inkDot 1.5s ease-in-out infinite 0.1s; }
.dot-3 { animation: inkDot 1.5s ease-in-out infinite 0.2s; }
.dot-4 { animation: inkDot 1.5s ease-in-out infinite 0.3s; }
.dot-5 { animation: inkDot 1.5s ease-in-out infinite 0.4s; }

@keyframes inkDot {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

/* 旋转加载动画 */
.spinner-loading {
  width: 80rpx;
  height: 80rpx;
}

.spinner-ring {
  position: relative;
  width: 100%;
  height: 100%;
  animation: spinnerRotate 2s linear infinite;
}

.spinner-segment {
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  background: var(--loading-color);
  border-radius: 50%;
}

.spinner-segment:nth-child(1) {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  animation: spinnerFade 2s ease-in-out infinite 0s;
}

.spinner-segment:nth-child(2) {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  animation: spinnerFade 2s ease-in-out infinite 0.5s;
}

.spinner-segment:nth-child(3) {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  animation: spinnerFade 2s ease-in-out infinite 1s;
}

.spinner-segment:nth-child(4) {
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  animation: spinnerFade 2s ease-in-out infinite 1.5s;
}

@keyframes spinnerRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes spinnerFade {
  0%, 100% { opacity: 0.2; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1); }
}

/* 水波纹动画 */
.ripple-loading {
  position: relative;
  width: 100rpx;
  height: 100rpx;
}

.ripple-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid var(--loading-color);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.circle-1 { animation: rippleExpand 2s ease-out infinite 0s; }
.circle-2 { animation: rippleExpand 2s ease-out infinite 0.5s; }
.circle-3 { animation: rippleExpand 2s ease-out infinite 1s; }

@keyframes rippleExpand {
  0% {
    width: 20rpx;
    height: 20rpx;
    opacity: 1;
  }
  100% {
    width: 100rpx;
    height: 100rpx;
    opacity: 0;
  }
}

/* 加载文字 */
.loading-text {
  margin-top: 24rpx;
}

.text-content {
  color: var(--loading-color);
  font-size: 26rpx;
  font-weight: 400;
  letter-spacing: 1rpx;
}

/* 尺寸变化 */
.ink-loading.small {
  transform: scale(0.8);
}

.ink-loading.small .text-content {
  font-size: 22rpx;
}

.ink-loading.large {
  transform: scale(1.2);
}

.ink-loading.large .text-content {
  font-size: 30rpx;
}
