// pages/index/index.js - 主界面
const app = getApp();

Page({
  data: {
    userInfo: null,
    credits: 0,
    isLoggedIn: false,
    showInviteModal: false,
    inviteCode: '',

    // 功能模块列表
    functionList: [
      {
        id: 'yijing',
        title: '周易卦象',
        subtitle: '朱熹《本义》',
        credits: 3,
        icon: '☰',
        description: '基于49部易经典籍+21卷古今文全集',
        color: 'linear-gradient(135deg, #1a1a1a 0%, #333333 100%)',
        route: '/pages/yijing/yijing'
      },
      {
        id: 'bazi',
        title: '子平八字',
        subtitle: '《子平遗书》413册',
        credits: 2,
        icon: '八',
        description: '基于明抄本权威典籍的四柱推命',
        color: 'linear-gradient(135deg, #666666 0%, #888888 100%)',
        route: '/pages/bazi/bazi'
      },
      {
        id: 'ziwei',
        title: '紫微斗数',
        subtitle: '陈希夷原著',
        credits: 3,
        icon: '紫',
        description: '基于300+部典籍全面分析',
        color: 'linear-gradient(135deg, #1a1a1a 0%, #333333 100%)',
        route: '/pages/ziwei/ziwei'
      },
      {
        id: 'meihua',
        title: '梅花易数',
        subtitle: '邵雍原著',
        credits: 1,
        icon: '梅',
        description: '宋代原著，梅花易数鼻祖',
        color: 'linear-gradient(135deg, #999999 0%, #aaaaaa 100%)',
        route: '/pages/meihua/meihua'
      }
    ],

    // 免费功能
    freeFeatures: [
      {
        id: 'knowledge',
        title: '古籍查询',
        subtitle: '免费',
        icon: '书',
        description: '免费查阅所有古籍原文内容',
        route: '/pages/knowledge/knowledge'
      }
    ]
  },
  onLoad() {
    console.log('主界面加载');
    this.initPage();
  },

  onShow() {
    this.refreshUserInfo();
  },

  // 初始化页面
  initPage() {
    this.refreshUserInfo();
    this.checkFirstVisit();
  },

  // 刷新用户信息
  refreshUserInfo() {
    const userInfo = app.globalData.userInfo;
    const credits = app.globalData.credits || 0;

    this.setData({
      userInfo: userInfo,
      credits: credits,
      isLoggedIn: !!userInfo
    });
  },

  // 检查首次访问
  checkFirstVisit() {
    const isFirstVisit = wx.getStorageSync('isFirstVisit');
    if (!isFirstVisit) {
      wx.setStorageSync('isFirstVisit', true);
      // 可以显示引导页面或提示
    }
  },

  // 点击功能模块
  onClickFunction(e) {
    const { id, credits, route } = e.currentTarget.dataset;

    // 检查是否登录
    if (!this.data.isLoggedIn) {
      this.showLoginTip();
      return;
    }

    // 检查功能是否已开发
    if (id !== 'meihua') {
      wx.showModal({
        title: '功能开发中',
        content: '该功能正在紧张开发中，敬请期待！',
        showCancel: false,
        confirmText: '确定'
      });
      return;
    }

    // 检查积分是否足够
    if (this.data.credits < credits) {
      this.showInsufficientCredits(credits);
      return;
    }

    // 跳转到对应功能页面
    wx.navigateTo({
      url: route
    });
  },

  // 点击免费功能
  onClickFreeFeature(e) {
    const { route } = e.currentTarget.dataset;
    wx.navigateTo({
      url: route
    });
  },

  // 显示登录提示
  showLoginTip() {
    wx.showModal({
      title: '需要登录',
      content: '请先登录后使用此功能',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/splash/splash'
          });
        }
      }
    });
  },

  // 显示积分不足提示
  showInsufficientCredits(needCredits) {
    wx.showModal({
      title: '积分不足',
      content: `此功能需要${needCredits}积分，您当前有${this.data.credits}积分`,
      confirmText: '去充值',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/recharge/recharge'
          });
        }
      }
    });
  },

  // 显示邀请好友弹窗
  onShowInvite() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip();
      return;
    }

    this.setData({
      showInviteModal: true,
      inviteCode: this.generateInviteCode()
    });
  },

  // 关闭邀请弹窗
  onCloseInvite() {
    this.setData({
      showInviteModal: false
    });
  },

  // 分享邀请
  onShareInvite() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 生成邀请码
  generateInviteCode() {
    // 这里应该从服务器获取用户的邀请码
    return 'ABC123';
  },

  // 跳转充值页面
  onGoRecharge() {
    wx.navigateTo({
      url: '/pages/recharge/recharge'
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '元亨利贞 - 千年古籍AI智慧',
      path: '/pages/splash/splash',
      imageUrl: '/images/share-cover.jpg'
    };
  }
});
