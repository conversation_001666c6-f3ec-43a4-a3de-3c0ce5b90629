// pages/splash/splash.js - 启动页面
const app = getApp();

Page({
  data: {
    showLogo: false,
    showTitle: false,
    showSubtitle: false,
    showButton: false,
    animationStep: 0,
    userInfo: null,
    hasUserInfo: false
  },

  onLoad() {
    console.log('启动页面加载');
    this.startAnimation();
  },

  onShow() {
    // 检查是否已经登录
    this.checkLoginStatus();
  },

  // 开始启动动画
  startAnimation() {
    // 延迟显示各个元素，营造水墨渐现效果
    setTimeout(() => {
      this.setData({ showLogo: true, animationStep: 1 });
    }, 500);

    setTimeout(() => {
      this.setData({ showTitle: true, animationStep: 2 });
    }, 1200);

    setTimeout(() => {
      this.setData({ showSubtitle: true, animationStep: 3 });
    }, 1800);

    setTimeout(() => {
      this.setData({ showButton: true, animationStep: 4 });
    }, 2500);
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = app.globalData.userInfo;
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      });
    }
  },

  // 微信授权登录
  async onWechatLogin() {
    try {
      app.showLoading('正在登录...');
      
      // 获取用户信息
      const userInfo = await app.getUserInfo();
      console.log('获取用户信息成功:', userInfo);
      
      // 获取OpenID
      const openid = await app.getOpenId();
      console.log('获取OpenID成功:', openid);
      
      // 检查用户是否已注册
      const userRecord = await this.checkUserRegistration(openid);
      
      if (!userRecord) {
        // 新用户注册
        await this.registerNewUser(openid, userInfo);
      } else {
        // 老用户登录
        app.globalData.credits = userRecord.credits;
        app.globalData.phone = userRecord.phone;
      }
      
      app.hideLoading();
      app.showSuccess('登录成功');
      
      // 跳转到主页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);
      
    } catch (error) {
      app.hideLoading();
      console.error('登录失败:', error);
      app.showError('登录失败，请重试');
    }
  },

  // 检查用户注册状态
  checkUserRegistration(openid) {
    return new Promise((resolve, reject) => {
      wx.cloud.database().collection('users').where({
        openid: openid
      }).get({
        success: (res) => {
          resolve(res.data.length > 0 ? res.data[0] : null);
        },
        fail: reject
      });
    });
  },

  // 注册新用户
  registerNewUser(openid, userInfo) {
    return new Promise((resolve, reject) => {
      const now = new Date();
      const userData = {
        openid: openid,
        nickname: userInfo.nickName,
        avatar: userInfo.avatarUrl,
        credits: app.globalData.registerCredits, // 注册赠送10积分
        phone: null,
        registerTime: now,
        lastLoginTime: now,
        inviteCode: this.generateInviteCode(),
        totalInvites: 0
      };

      wx.cloud.database().collection('users').add({
        data: userData,
        success: (res) => {
          console.log('新用户注册成功:', res);
          app.globalData.credits = userData.credits;
          resolve(res);
        },
        fail: reject
      });
    });
  },

  // 生成邀请码
  generateInviteCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  // 跳过登录，直接进入（游客模式）
  onSkipLogin() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});
