/* pages/recharge/recharge.wxss - 充值页面样式 */

page {
  background: var(--paper-white);
  padding-bottom: 200rpx;
}

.recharge-container {
  min-height: 100vh;
  padding: 40rpx 30rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.header-title {
  font-size: 48rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: var(--ink-gray);
}

/* 当前积分显示 */
.credits-display {
  margin-bottom: 40rpx;
}

.credits-card {
  background: linear-gradient(135deg, var(--ancient-gold) 0%, #f0d060 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(212, 175, 55, 0.3);
}

.credits-icon {
  font-size: 60rpx;
  margin-right: 30rpx;
}

.credits-info {
  flex: 1;
}

.credits-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.credits-value {
  color: white;
  font-size: 48rpx;
  font-weight: 600;
}

/* 充值套餐区域 */
.packages-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 30rpx;
}

.packages-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.package-card {
  position: relative;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  border: 3rpx solid #f0f0f0;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.06);
  transition: all 0.3s ease;
}

.package-card.selected {
  border-color: var(--ancient-gold);
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.2);
}

.package-card.popular {
  border-color: var(--ink-black);
  box-shadow: 0 8rpx 24rpx rgba(26, 26, 26, 0.15);
}

.popular-tag {
  position: absolute;
  top: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  background: var(--ink-black);
  color: white;
  font-size: 20rpx;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.package-content {
  margin-top: 20rpx;
}

.package-credits {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 16rpx;
}

.package-price {
  margin-bottom: 12rpx;
}

.current-price {
  font-size: 36rpx;
  color: var(--ancient-gold);
  font-weight: 600;
}

.original-price {
  font-size: 24rpx;
  color: var(--ink-light);
  text-decoration: line-through;
  margin-left: 8rpx;
}

.package-discount {
  font-size: 22rpx;
  color: var(--ink-gray);
}

.selected-indicator {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: var(--ancient-gold);
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  color: white;
  font-size: 24rpx;
  font-weight: 600;
}

/* 充值说明 */
.recharge-tips {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.06);
}

.tips-title {
  font-size: 28rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 20rpx;
}

.tips-list {
  line-height: 1.6;
}

.tip-item {
  font-size: 24rpx;
  color: var(--ink-gray);
  margin-bottom: 8rpx;
}

/* 功能消耗说明 */
.consumption-guide {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.06);
}

.guide-title {
  font-size: 28rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 20rpx;
}

.guide-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.guide-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.guide-item:last-child {
  border-bottom: none;
}

.guide-name {
  font-size: 26rpx;
  color: var(--ink-black);
}

.guide-cost {
  font-size: 24rpx;
  color: var(--ancient-gold);
  font-weight: 600;
}

/* 底部操作区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  box-shadow: 0 -4rpx 16rpx rgba(26, 26, 26, 0.1);
}

.selected-info {
  text-align: center;
  margin-bottom: 20rpx;
}

.selected-text {
  font-size: 26rpx;
  color: var(--ink-black);
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-button {
  flex: 1;
  padding: 24rpx;
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.1);
}

.primary-button {
  background: linear-gradient(135deg, var(--ancient-gold) 0%, #f0d060 100%);
}

.primary-button .action-text {
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

.secondary-button {
  background: white;
  border: 2rpx solid var(--ink-gray);
}

.secondary-button .action-text {
  color: var(--ink-gray);
  font-size: 28rpx;
  font-weight: 500;
}

.contact-service {
  text-align: center;
  padding: 16rpx;
}

.contact-text {
  font-size: 24rpx;
  color: var(--ink-light);
  text-decoration: underline;
}
