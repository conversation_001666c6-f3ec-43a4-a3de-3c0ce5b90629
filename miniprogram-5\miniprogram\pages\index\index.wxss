/* pages/index/index.wxss - 主界面样式 */

page {
  background: var(--paper-white);
  padding-bottom: 120rpx;
}

.index-container {
  min-height: 100vh;
  background: var(--paper-white);
}

/* 用户信息头部 */
.user-header {
  padding: 40rpx 30rpx;
  margin-bottom: 40rpx;
  border-radius: 0 0 40rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  overflow: hidden;
  background: var(--ink-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.guest-avatar {
  background: linear-gradient(135deg, var(--ink-gray) 0%, var(--ink-light) 100%);
}

.guest-icon {
  color: white;
  font-size: 32rpx;
  font-weight: 500;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 500;
  margin-bottom: 8rpx;
}

.user-credits {
  display: flex;
  align-items: center;
}

.credits-label {
  font-size: 28rpx;
  color: var(--ink-gray);
}

.credits-value {
  font-size: 32rpx;
  color: var(--ancient-gold);
  font-weight: 600;
  margin-left: 8rpx;
}

/* 邀请好友按钮 */
.invite-section {
  margin-top: 20rpx;
}

.invite-button {
  background: linear-gradient(135deg, var(--ink-black) 0%, #333333 100%);
  padding: 20rpx 30rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.15);
}

.invite-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

.invite-icon {
  color: var(--ancient-gold);
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  background: rgba(212, 175, 55, 0.2);
  border-radius: 20rpx;
}

/* 登录提示 */
.login-tip {
  margin-top: 20rpx;
}

.login-button {
  background: linear-gradient(135deg, var(--ink-gray) 0%, var(--ink-light) 100%);
  padding: 20rpx 30rpx;
  border-radius: 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(102, 102, 102, 0.15);
}

.login-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

/* 主标题区域 */
.main-title {
  padding: 0 30rpx;
  margin-bottom: 60rpx;
  text-align: center;
}

.title-text {
  font-size: 40rpx;
  color: var(--ink-black);
  font-weight: 500;
  margin-bottom: 20rpx;
}

.title-decoration {
  width: 200rpx;
  height: 3rpx;
  margin: 0 auto;
}

/* 功能区域标题 */
.section-header {
  padding: 0 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  color: var(--ink-black);
  font-weight: 500;
  font-family: 'STSong', '华文宋体', serif;
}

/* 功能模块网格 */
.function-grid {
  padding: 0 30rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 60rpx;
}

/* 功能卡片 */
.function-card {
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.08);
  background: white;
  min-height: 200rpx;
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.9;
}

.card-content {
  position: relative;
  z-index: 2;
  padding: 30rpx 24rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.card-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.icon-text {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  font-family: 'STKaiti', '楷体', serif;
}

.card-info {
  text-align: center;
}

.card-title {
  color: white;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 6rpx;
}

.card-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 22rpx;
  margin-bottom: 8rpx;
}

.card-credits {
  color: var(--ancient-gold);
  font-size: 24rpx;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.card-description {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  padding: 16rpx;
  z-index: 3;
}

.description-text {
  color: white;
  font-size: 24rpx;
  line-height: 1.4;
  text-align: center;
  font-weight: 500;
}

/* 免费功能区域 */
.free-section {
  margin-bottom: 60rpx;
}

.free-grid {
  padding: 0 30rpx;
}

.free-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.06);
  border: 2rpx solid var(--ancient-gold);
  margin-bottom: 20rpx;
}

.free-card-content {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.free-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--ancient-gold) 0%, #f0d060 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.free-icon-text {
  color: white;
  font-size: 24rpx;
  font-weight: 600;
}

.free-info {
  flex: 1;
}

.free-title {
  color: var(--ink-black);
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.free-subtitle {
  color: var(--ancient-gold);
  font-size: 22rpx;
  font-weight: 600;
}

.free-description {
  padding-left: 70rpx;
}

.free-desc-text {
  color: var(--ink-gray);
  font-size: 24rpx;
  line-height: 1.4;
}

/* 充值区域 */
.recharge-section {
  padding: 0 30rpx;
  margin-bottom: 40rpx;
}

.recharge-card {
  background: linear-gradient(135deg, var(--ancient-gold) 0%, #f0d060 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.3);
}

.recharge-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.recharge-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.recharge-info {
  flex: 1;
}

.recharge-title {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.recharge-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.recharge-arrow {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

/* 邀请弹窗 */
.invite-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background: white;
  border-radius: 20rpx;
  width: 600rpx;
  max-width: 90%;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 40rpx 30rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--ink-gray);
}

.modal-body {
  padding: 30rpx;
}

.invite-info {
  text-align: center;
  margin-bottom: 30rpx;
}

.invite-desc {
  color: var(--ink-gray);
  font-size: 28rpx;
  line-height: 1.5;
}

.invite-code-section {
  background: var(--ancient-paper);
  border-radius: 12rpx;
  padding: 30rpx;
  text-align: center;
  margin-bottom: 30rpx;
}

.code-label {
  color: var(--ink-gray);
  font-size: 24rpx;
  display: block;
  margin-bottom: 12rpx;
}

.code-value {
  color: var(--ink-black);
  font-size: 36rpx;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.modal-footer {
  padding: 20rpx 30rpx 40rpx;
}

.share-button {
  background: linear-gradient(135deg, var(--ink-black) 0%, #333333 100%);
  color: white;
  padding: 24rpx;
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.2);
}

.share-text {
  font-size: 28rpx;
  font-weight: 500;
}
