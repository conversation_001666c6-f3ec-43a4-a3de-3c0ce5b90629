@echo off
echo 正在启动微信开发者工具...
echo 项目路径: %cd%

REM 尝试常见的微信开发者工具安装路径
set "WECHAT_TOOL_PATH="

if exist "C:\Program Files (x86)\Tencent\微信web开发者工具\cli.bat" (
    set "WECHAT_TOOL_PATH=C:\Program Files (x86)\Tencent\微信web开发者工具\cli.bat"
) else if exist "C:\Program Files\Tencent\微信web开发者工具\cli.bat" (
    set "WECHAT_TOOL_PATH=C:\Program Files\Tencent\微信web开发者工具\cli.bat"
) else if exist "%USERPROFILE%\AppData\Local\微信web开发者工具\cli.bat" (
    set "WECHAT_TOOL_PATH=%USERPROFILE%\AppData\Local\微信web开发者工具\cli.bat"
)

if defined WECHAT_TOOL_PATH (
    echo 找到微信开发者工具: %WECHAT_TOOL_PATH%
    "%WECHAT_TOOL_PATH%" open --project "%cd%"
) else (
    echo 未找到微信开发者工具，请手动打开微信开发者工具并导入此项目
    echo 项目路径: %cd%
    pause
)
