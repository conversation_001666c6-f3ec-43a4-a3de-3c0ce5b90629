{"name": "@protobufjs/float", "description": "Reads / writes floats / doubles from / to buffers in both modern and ancient browsers.", "version": "1.0.2", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/dcodeIO/protobuf.js.git"}, "dependencies": {}, "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "types": "index.d.ts", "devDependencies": {"benchmark": "^2.1.4", "chalk": "^1.1.3", "ieee754": "^1.1.8", "istanbul": "^0.4.5", "tape": "^4.6.3"}, "scripts": {"test": "tape tests/*.js", "coverage": "istanbul cover node_modules/tape/bin/tape tests/*.js", "bench": "node bench"}}