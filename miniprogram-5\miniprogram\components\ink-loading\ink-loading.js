// components/ink-loading/ink-loading.js - 水墨风加载动画组件
Component({
  properties: {
    // 加载类型：spinner, dots, brush, ripple
    type: {
      type: String,
      value: 'brush'
    },
    // 加载大小：small, medium, large
    size: {
      type: String,
      value: 'medium'
    },
    // 加载文字
    text: {
      type: String,
      value: '加载中...'
    },
    // 是否显示文字
    showText: {
      type: Boolean,
      value: true
    },
    // 自定义颜色
    color: {
      type: String,
      value: ''
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  data: {
    // 动画状态
    animating: true
  },

  lifetimes: {
    attached() {
      this.startAnimation();
    },

    detached() {
      this.stopAnimation();
    }
  },

  methods: {
    // 开始动画
    startAnimation() {
      this.setData({
        animating: true
      });
    },

    // 停止动画
    stopAnimation() {
      this.setData({
        animating: false
      });
    }
  }
});
