<!--pages/components-demo/components-demo.wxml - 水墨风组件演示页面-->
<view class="demo-container">
  <!-- 页面标题 -->
  <view class="demo-header">
    <text class="demo-title">水墨风组件库</text>
    <text class="demo-subtitle">优雅的中式UI组件演示</text>
  </view>

  <!-- 按钮组件演示 -->
  <view class="demo-section">
    <text class="section-title">按钮组件</text>
    
    <view class="button-group">
      <ink-button 
        text="主要按钮" 
        type="primary" 
        size="medium"
        bind:tap="onButtonTap">
      </ink-button>
      
      <ink-button 
        text="次要按钮" 
        type="secondary" 
        size="medium"
        bind:tap="onButtonTap">
      </ink-button>
      
      <ink-button 
        text="幽灵按钮" 
        type="ghost" 
        size="medium"
        bind:tap="onButtonTap">
      </ink-button>
    </view>

    <view class="button-group">
      <ink-button 
        text="大按钮" 
        type="primary" 
        size="large"
        bind:tap="onButtonTap">
      </ink-button>
    </view>

    <view class="button-group">
      <ink-button 
        text="小按钮" 
        type="primary" 
        size="small"
        bind:tap="onButtonTap">
      </ink-button>
      
      <ink-button 
        text="加载中" 
        type="primary" 
        size="small"
        loading="{{loading}}"
        bind:tap="toggleLoading">
      </ink-button>
    </view>
  </view>

  <!-- 输入框组件演示 -->
  <view class="demo-section">
    <text class="section-title">输入框组件</text>
    
    <ink-input
      label="用户名"
      placeholder="请输入用户名"
      value="{{inputValue}}"
      required="{{true}}"
      bind:input="onInputChange">
    </ink-input>
    
    <ink-input
      label="密码"
      type="password"
      placeholder="请输入密码">
    </ink-input>
    
    <ink-input
      label="备注"
      placeholder="请输入备注信息"
      maxlength="100">
    </ink-input>
  </view>

  <!-- 卡片组件演示 -->
  <view class="demo-section">
    <text class="section-title">卡片组件</text>
    
    <ink-card 
      title="{{cardData.title}}"
      subtitle="{{cardData.subtitle}}"
      clickable="{{true}}"
      bind:tap="onCardTap">
      <view slot="">
        <text class="card-content">这是一个水墨风格的卡片组件，具有优雅的中式设计风格。支持标题、副标题、自定义内容等功能。</text>
      </view>
      <view slot="footer">
        <ink-button text="了解更多" type="ghost" size="small"></ink-button>
      </view>
    </ink-card>

    <ink-card type="bordered" shadow="{{false}}">
      <view slot="">
        <text class="card-content">这是一个带边框的卡片，没有阴影效果。</text>
      </view>
    </ink-card>
  </view>

  <!-- 加载动画演示 -->
  <view class="demo-section">
    <text class="section-title">加载动画</text>
    
    <view class="loading-group">
      <view class="loading-item">
        <ink-loading type="brush" text="毛笔书写"></ink-loading>
      </view>
      
      <view class="loading-item">
        <ink-loading type="dots" text="水墨扩散"></ink-loading>
      </view>
      
      <view class="loading-item">
        <ink-loading type="spinner" text="旋转加载"></ink-loading>
      </view>
      
      <view class="loading-item">
        <ink-loading type="ripple" text="水波纹"></ink-loading>
      </view>
    </view>
  </view>

  <!-- 返回按钮 -->
  <view class="demo-footer">
    <ink-button 
      text="返回主页" 
      type="secondary" 
      size="large"
      bind:tap="goBack">
    </ink-button>
  </view>
</view>
