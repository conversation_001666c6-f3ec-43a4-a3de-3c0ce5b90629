/* components/ink-card/ink-card.wxss - 水墨风卡片样式 */

.ink-card {
  position: relative;
  background: var(--paper-white);
  border-radius: 16rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  font-family: 'STSong', '华文宋体', serif;
}

/* 水墨纹理背景 */
.ink-texture {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 30%, rgba(26, 26, 26, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(26, 26, 26, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(26, 26, 26, 0.01) 0%, transparent 50%);
  pointer-events: none;
}

/* 卡片内容 */
.card-content {
  position: relative;
  z-index: 2;
  padding: 32rpx;
}

/* 卡片头部 */
.card-header {
  margin-bottom: 24rpx;
}

.card-title {
  color: var(--ink-black);
  font-size: 32rpx;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.card-subtitle {
  color: var(--ink-gray);
  font-size: 24rpx;
  line-height: 1.4;
}

/* 卡片主体 */
.card-body {
  color: var(--ink-black);
  font-size: 28rpx;
  line-height: 1.6;
}

/* 卡片底部 */
.card-footer {
  margin-top: 24rpx;
}

/* 水墨装饰边框 */
.ink-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1rpx solid rgba(26, 26, 26, 0.1);
  border-radius: inherit;
  pointer-events: none;
}

/* 卡片类型样式 */
.ink-card.default {
  background: var(--paper-white);
}

.ink-card.bordered {
  border: 2rpx solid var(--ink-light);
}

.ink-card.elevated {
  background: linear-gradient(145deg, #ffffff 0%, #f8f8f8 100%);
}

/* 阴影效果 */
.ink-card.shadow {
  box-shadow: 
    0 8rpx 32rpx rgba(26, 26, 26, 0.08),
    0 2rpx 8rpx rgba(26, 26, 26, 0.04);
}

/* 可点击状态 */
.ink-card.clickable {
  cursor: pointer;
}

.ink-card.clickable:hover {
  transform: translateY(-4rpx);
  box-shadow: 
    0 16rpx 48rpx rgba(26, 26, 26, 0.12),
    0 4rpx 16rpx rgba(26, 26, 26, 0.08);
}

/* 按下状态 */
.ink-card.pressed {
  transform: scale(0.98);
  box-shadow: 
    0 4rpx 16rpx rgba(26, 26, 26, 0.06),
    0 1rpx 4rpx rgba(26, 26, 26, 0.04);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .card-content {
    padding: 24rpx;
  }
  
  .card-title {
    font-size: 28rpx;
  }
  
  .card-subtitle {
    font-size: 22rpx;
  }
  
  .card-body {
    font-size: 26rpx;
  }
}
