/* pages/components-demo/components-demo.wxss - 水墨风组件演示页面样式 */

.demo-container {
  min-height: 100vh;
  background: var(--ancient-paper);
  padding: 32rpx;
  font-family: 'STSong', '华文宋体', serif;
}

/* 页面标题 */
.demo-header {
  text-align: center;
  margin-bottom: 48rpx;
  padding: 32rpx 0;
  border-bottom: 2rpx solid var(--ink-light);
}

.demo-title {
  display: block;
  color: var(--ink-black);
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  letter-spacing: 2rpx;
}

.demo-subtitle {
  display: block;
  color: var(--ink-gray);
  font-size: 28rpx;
  letter-spacing: 1rpx;
}

/* 演示区块 */
.demo-section {
  margin-bottom: 64rpx;
  background: var(--paper-white);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
}

.section-title {
  display: block;
  color: var(--ink-black);
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid var(--ink-light);
  letter-spacing: 1rpx;
}

/* 按钮组 */
.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.button-group:last-child {
  margin-bottom: 0;
}

/* 卡片内容 */
.card-content {
  color: var(--ink-black);
  font-size: 28rpx;
  line-height: 1.6;
  letter-spacing: 0.5rpx;
}

/* 加载动画组 */
.loading-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
}

.loading-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx;
  background: var(--ancient-paper);
  border-radius: 12rpx;
  border: 1rpx solid var(--ink-light);
}

/* 页面底部 */
.demo-footer {
  margin-top: 64rpx;
  text-align: center;
  padding-top: 32rpx;
  border-top: 2rpx solid var(--ink-light);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .demo-container {
    padding: 24rpx;
  }
  
  .demo-title {
    font-size: 40rpx;
  }
  
  .demo-subtitle {
    font-size: 24rpx;
  }
  
  .section-title {
    font-size: 32rpx;
  }
  
  .loading-group {
    grid-template-columns: 1fr;
  }
}
