// pages/components-demo/components-demo.js - 水墨风组件演示页面
const app = getApp();

Page({
  data: {
    inputValue: '',
    loading: false,
    cardData: {
      title: '水墨风卡片',
      subtitle: '优雅的中式设计'
    }
  },

  onLoad() {
    console.log('组件演示页面加载');
  },

  // 按钮点击事件
  onButtonTap(e) {
    console.log('按钮被点击:', e);
    wx.showToast({
      title: '按钮点击成功',
      icon: 'success'
    });
  },

  // 输入框输入事件
  onInputChange(e) {
    console.log('输入内容:', e.detail.value);
    this.setData({
      inputValue: e.detail.value
    });
  },

  // 卡片点击事件
  onCardTap(e) {
    console.log('卡片被点击:', e);
    wx.showToast({
      title: '卡片点击成功',
      icon: 'success'
    });
  },

  // 切换加载状态
  toggleLoading() {
    this.setData({
      loading: !this.data.loading
    });
  },

  // 返回主页
  goBack() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});
