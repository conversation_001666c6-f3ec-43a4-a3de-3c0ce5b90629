<!--pages/index/index.wxml - 主界面模板-->
<view class="index-container">
  <!-- 顶部用户信息区域 -->
  <view class="user-header ink-gradient-bg">
    <view class="user-info">
      <view class="user-avatar" wx:if="{{isLoggedIn}}">
        <image src="{{userInfo.avatarUrl}}" class="avatar-img"></image>
      </view>
      <view class="user-avatar guest-avatar" wx:else>
        <text class="guest-icon">客</text>
      </view>

      <view class="user-details">
        <view class="user-name">
          <text wx:if="{{isLoggedIn}}">用户：{{userInfo.nickName}}</text>
          <text wx:else>游客模式</text>
        </view>
        <view class="user-credits">
          <text class="credits-label">积分：</text>
          <text class="credits-value">{{credits}}</text>
        </view>
      </view>
    </view>

    <!-- 邀请好友按钮 -->
    <view class="invite-section" wx:if="{{isLoggedIn}}">
      <view class="invite-button ink-ripple" bindtap="onShowInvite">
        <text class="invite-text">邀请好友获50积分</text>
        <text class="invite-icon">分享</text>
      </view>
    </view>

    <!-- 登录提示 -->
    <view class="login-tip" wx:else>
      <view class="login-button ink-ripple" bindtap="showLoginTip">
        <text class="login-text">登录获取积分</text>
      </view>
    </view>
  </view>

  <!-- 主标题区域 -->
  <view class="main-title">
    <view class="title-text ink-title">元亨利贞 • 古籍智慧传承</view>
    <view class="title-decoration ink-divider"></view>
  </view>

  <!-- 古籍命理模块 -->
  <view class="section-header">
    <text class="section-title">古籍命理模块</text>
  </view>

  <!-- 功能模块网格 -->
  <view class="function-grid">
    <view
      class="function-card ink-fade-in ink-ripple"
      wx:for="{{functionList}}"
      wx:key="id"
      data-id="{{item.id}}"
      data-credits="{{item.credits}}"
      data-route="{{item.route}}"
      bindtap="onClickFunction"
    >
      <view class="card-background" style="background: {{item.color}};"></view>
      <view class="card-content">
        <view class="card-icon">
          <text class="icon-text">{{item.icon}}</text>
        </view>
        <view class="card-info">
          <view class="card-title">{{item.title}}</view>
          <view class="card-subtitle">{{item.subtitle}}</view>
          <view class="card-credits">{{item.credits}}积分</view>
        </view>
      </view>
      <view class="card-description">
        <text class="description-text">{{item.description}}</text>
      </view>
    </view>
  </view>

  <!-- 免费功能区域 -->
  <view class="free-section">
    <view class="section-header">
      <text class="section-title">免费功能</text>
    </view>

    <view class="free-grid">
      <view
        class="free-card ink-fade-in ink-ripple"
        wx:for="{{freeFeatures}}"
        wx:key="id"
        data-route="{{item.route}}"
        bindtap="onClickFreeFeature"
      >
        <view class="free-card-content">
          <view class="free-icon">
            <text class="free-icon-text">{{item.icon}}</text>
          </view>
          <view class="free-info">
            <view class="free-title">{{item.title}}</view>
            <view class="free-subtitle">{{item.subtitle}}</view>
          </view>
        </view>
        <view class="free-description">
          <text class="free-desc-text">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 积分充值入口 -->
  <view class="recharge-section">
    <view class="recharge-card ink-ripple" bindtap="onGoRecharge">
      <view class="recharge-content">
        <view class="recharge-icon">💰</view>
        <view class="recharge-info">
          <view class="recharge-title">积分充值</view>
          <view class="recharge-subtitle">微信支付</view>
        </view>
        <view class="recharge-arrow">→</view>
      </view>
    </view>
  </view>

  <!-- 邀请好友弹窗 -->
  <view class="invite-modal" wx:if="{{showInviteModal}}">
    <view class="modal-mask" bindtap="onCloseInvite"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">邀请好友</text>
        <view class="modal-close" bindtap="onCloseInvite">×</view>
      </view>
      <view class="modal-body">
        <view class="invite-info">
          <text class="invite-desc">邀请好友注册可获得50积分奖励</text>
        </view>
        <view class="invite-code-section">
          <text class="code-label">您的邀请码：</text>
          <text class="code-value">{{inviteCode}}</text>
        </view>
      </view>
      <view class="modal-footer">
        <view class="share-button ink-ripple" bindtap="onShareInvite">
          <text class="share-text">分享给好友</text>
        </view>
      </view>
    </view>
  </view>
</view>