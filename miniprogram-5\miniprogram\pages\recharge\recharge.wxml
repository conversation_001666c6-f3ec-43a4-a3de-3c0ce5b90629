<!--pages/recharge/recharge.wxml - 充值页面模板-->
<view class="recharge-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">积分充值</view>
    <view class="header-subtitle">微信安全支付</view>
  </view>

  <!-- 当前积分显示 -->
  <view class="credits-display">
    <view class="credits-card">
      <view class="credits-icon">💰</view>
      <view class="credits-info">
        <view class="credits-label">当前积分</view>
        <view class="credits-value">{{currentCredits}}</view>
      </view>
    </view>
  </view>

  <!-- 充值套餐 -->
  <view class="packages-section">
    <view class="section-title">选择充值套餐</view>
    
    <view class="packages-grid">
      <view 
        class="package-card {{selectedOption && selectedOption.id === item.id ? 'selected' : ''}} {{item.popular ? 'popular' : ''}}"
        wx:for="{{rechargeOptions}}"
        wx:key="id"
        data-option="{{item}}"
        bindtap="onSelectOption"
      >
        <!-- 推荐标签 -->
        <view class="popular-tag" wx:if="{{item.popular}}">{{item.discount}}</view>
        
        <view class="package-content">
          <view class="package-credits">{{item.credits}}积分</view>
          <view class="package-price">
            <text class="current-price">¥{{item.price}}</text>
            <text class="original-price" wx:if="{{item.originalPrice !== item.price}}">¥{{item.originalPrice}}</text>
          </view>
          <view class="package-discount" wx:if="{{!item.popular}}">{{item.discount}}</view>
        </view>
        
        <!-- 选中状态 -->
        <view class="selected-indicator" wx:if="{{selectedOption && selectedOption.id === item.id}}">
          <text class="check-icon">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 充值说明 */
  <view class="recharge-tips">
    <view class="tips-title">充值说明</view>
    <view class="tips-list">
      <view class="tip-item">• 积分永久有效，无过期时间</view>
      <view class="tip-item">• 支持微信安全支付，资金有保障</view>
      <view class="tip-item">• 充值成功后积分立即到账</view>
      <view class="tip-item">• 如有问题请联系客服处理</view>
    </view>
  </view>

  <!-- 功能消耗说明 */
  <view class="consumption-guide">
    <view class="guide-title">积分消耗参考</view>
    <view class="guide-list">
      <view class="guide-item">
        <view class="guide-name">梅花易数</view>
        <view class="guide-cost">1积分/次</view>
      </view>
      <view class="guide-item">
        <view class="guide-name">子平八字</view>
        <view class="guide-cost">2积分/次</view>
      </view>
      <view class="guide-item">
        <view class="guide-name">周易卦象</view>
        <view class="guide-cost">3积分/次</view>
      </view>
      <view class="guide-item">
        <view class="guide-name">紫微斗数</view>
        <view class="guide-cost">3积分/次</view>
      </view>
    </view>
  </view>

  <!-- 底部操作区域 -->
  <view class="bottom-actions">
    <view class="selected-info" wx:if="{{selectedOption}}">
      <view class="selected-text">已选择：{{selectedOption.credits}}积分 - ¥{{selectedOption.price}}</view>
    </view>
    
    <view class="action-buttons">
      <view class="action-button secondary-button ink-ripple" bindtap="onViewHistory">
        <text class="action-text">充值记录</text>
      </view>
      
      <view class="action-button primary-button ink-ripple" bindtap="onRecharge">
        <text class="action-text">立即充值</text>
      </view>
    </view>
    
    <view class="contact-service" bindtap="onContactService">
      <text class="contact-text">遇到问题？联系客服</text>
    </view>
  </view>
</view>
