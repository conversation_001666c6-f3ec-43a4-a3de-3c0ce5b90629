// components/ink-button/ink-button.js - 水墨风按钮组件
Component({
  properties: {
    // 按钮类型：primary, secondary, ghost, danger
    type: {
      type: String,
      value: 'primary'
    },
    // 按钮大小：large, medium, small
    size: {
      type: String,
      value: 'medium'
    },
    // 按钮文字
    text: {
      type: String,
      value: '确定'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    // 是否圆角
    round: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // 按钮按下状态
    pressed: false
  },

  methods: {
    // 按钮点击事件
    onTap(e) {
      if (this.data.disabled || this.data.loading) {
        return;
      }
      
      // 触发父组件事件
      this.triggerEvent('tap', e.detail);
    },

    // 按钮按下
    onTouchStart() {
      if (this.data.disabled || this.data.loading) {
        return;
      }
      this.setData({ pressed: true });
    },

    // 按钮松开
    onTouchEnd() {
      this.setData({ pressed: false });
    },

    // 按钮取消
    onTouchCancel() {
      this.setData({ pressed: false });
    }
  }
});
