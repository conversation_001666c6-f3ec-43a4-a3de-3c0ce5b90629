<!--pages/meihua/meihua.wxml - 梅花易数页面模板-->
<view class="meihua-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">梅花易数</view>
    <view class="header-subtitle">邵雍原著 • 时间起卦</view>
  </view>

  <!-- 当前时间显示 -->
  <view class="time-section">
    <view class="time-label">当前时间</view>
    <view class="time-display">{{currentTime}}</view>
    <view class="time-tip">梅花易数以时间为基础进行起卦</view>
  </view>

  <!-- 起卦按钮 -->
  <view class="action-section" wx:if="{{!hexagram && !isAnalyzing}}">
    <view class="start-button ink-ripple" bindtap="onTimeHexagram">
      <view class="button-icon">🌸</view>
      <view class="button-text">时间起卦</view>
      <view class="button-cost">消耗1积分</view>
    </view>
  </view>

  <!-- 分析中状态 -->
  <view class="analyzing-section" wx:if="{{isAnalyzing}}">
    <view class="analyzing-animation">
      <view class="ink-drop"></view>
      <view class="ink-drop"></view>
      <view class="ink-drop"></view>
    </view>
    <view class="analyzing-text">正在起卦分析中...</view>
  </view>

  <!-- 卦象结果 -->
  <view class="hexagram-section" wx:if="{{hexagram}}">
    <view class="hexagram-header">
      <view class="hexagram-title">卦象结果</view>
      <view class="hexagram-time">起卦时间：{{hexagram.time}}</view>
    </view>

    <view class="hexagram-display">
      <view class="trigram-container">
        <view class="trigram upper-trigram">
          <view class="trigram-symbol">{{hexagram.upper.symbol}}</view>
          <view class="trigram-name">{{hexagram.upper.name}}卦</view>
          <view class="trigram-label">上卦</view>
        </view>
        
        <view class="hexagram-center">
          <view class="change-line">变爻：第{{hexagram.change}}爻</view>
        </view>
        
        <view class="trigram lower-trigram">
          <view class="trigram-symbol">{{hexagram.lower.symbol}}</view>
          <view class="trigram-name">{{hexagram.lower.name}}卦</view>
          <view class="trigram-label">下卦</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 分析结果 -->
  <view class="analysis-section" wx:if="{{analysis}}">
    <view class="analysis-header">
      <view class="analysis-title">AI智能解析</view>
    </view>
    
    <view class="analysis-content">
      <text class="analysis-text">{{analysis}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="bottom-actions" wx:if="{{hexagram}}">
    <view class="action-button secondary-button ink-ripple" bindtap="onRestart">
      <text class="action-text">重新起卦</text>
    </view>
  </view>
</view>
