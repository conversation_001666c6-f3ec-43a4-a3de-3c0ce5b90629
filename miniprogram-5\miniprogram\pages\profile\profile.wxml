<!--pages/profile/profile.wxml - 个人中心页面模板-->
<view class="profile-container">
  <!-- 用户信息头部 -->
  <view class="user-header">
    <view class="user-avatar">
      <image
        class="avatar-img"
        src="{{userInfo.avatarUrl || '/images/icons/avatar.png'}}"
        wx:if="{{isLoggedIn}}"
      />
      <view class="guest-avatar" wx:else>
        <text class="guest-icon">👤</text>
      </view>
    </view>

    <view class="user-info">
      <view class="user-name">{{isLoggedIn ? userInfo.nickName : '未登录'}}</view>
      <view class="user-credits" wx:if="{{isLoggedIn}}">
        <text class="credits-label">当前积分：</text>
        <text class="credits-value">{{credits}}</text>
      </view>
      <view class="login-tip" wx:else>
        <text class="tip-text">登录后享受更多服务</text>
      </view>
    </view>

    <view class="login-button ink-ripple" bindtap="onToggleLogin">
      <text class="login-text">{{isLoggedIn ? '注销' : '登录'}}</text>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view
      class="menu-item ink-ripple"
      wx:for="{{menuItems}}"
      wx:key="id"
      data-item="{{item}}"
      bindtap="onClickMenuItem"
    >
      <view class="menu-icon">{{item.icon}}</view>
      <view class="menu-title">{{item.title}}</view>
      <view class="menu-arrow">></view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <view class="version-text">元亨利贞 v1.0.0</view>
    <view class="copyright-text">© 2024 千年古籍AI智慧</view>
  </view>
</view>