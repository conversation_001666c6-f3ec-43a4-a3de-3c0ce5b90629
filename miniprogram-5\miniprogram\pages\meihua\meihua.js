// pages/meihua/meihua.js - 梅花易数页面
const app = getApp();

Page({
  data: {
    currentTime: '',
    hexagram: null,
    analysis: '',
    isAnalyzing: false
  },

  onLoad() {
    console.log('梅花易数页面加载');
    this.updateCurrentTime();
    
    // 每秒更新时间
    this.timeInterval = setInterval(() => {
      this.updateCurrentTime();
    }, 1000);
  },

  onUnload() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
  },

  // 更新当前时间
  updateCurrentTime() {
    const now = new Date();
    const timeStr = `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日 ${now.getHours()}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    
    this.setData({
      currentTime: timeStr
    });
  },

  // 时间起卦
  onTimeHexagram() {
    if (this.data.isAnalyzing) return;
    
    // 检查积分
    if (app.globalData.credits < 1) {
      wx.showModal({
        title: '积分不足',
        content: '梅花易数需要1积分，请先充值',
        confirmText: '去充值',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/recharge/recharge'
            });
          }
        }
      });
      return;
    }

    this.setData({
      isAnalyzing: true,
      hexagram: null,
      analysis: ''
    });

    // 模拟起卦过程
    this.generateHexagram();
  },

  // 生成卦象
  generateHexagram() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    const hour = now.getHours();
    const minute = now.getMinutes();

    // 简化的梅花易数算法
    const upperNum = (year + month + day) % 8;
    const lowerNum = (year + month + day + hour) % 8;
    const changeNum = (year + month + day + hour + minute) % 6;

    const trigrams = ['乾', '兑', '离', '震', '巽', '坎', '艮', '坤'];
    const trigramSymbols = ['☰', '☱', '☲', '☳', '☴', '☵', '☶', '☷'];

    const hexagram = {
      upper: {
        name: trigrams[upperNum],
        symbol: trigramSymbols[upperNum]
      },
      lower: {
        name: trigrams[lowerNum],
        symbol: trigramSymbols[lowerNum]
      },
      change: changeNum + 1,
      time: this.data.currentTime
    };

    setTimeout(() => {
      this.setData({
        hexagram: hexagram
      });
      
      // 模拟AI分析
      this.analyzeHexagram(hexagram);
    }, 1500);
  },

  // 分析卦象
  analyzeHexagram(hexagram) {
    // 这里应该调用DeepSeek API进行分析
    // 现在先用模拟数据
    setTimeout(() => {
      const analysis = `根据梅花易数分析：

上卦：${hexagram.upper.name}卦 ${hexagram.upper.symbol}
下卦：${hexagram.lower.name}卦 ${hexagram.lower.symbol}
变爻：第${hexagram.change}爻

卦象解析：
此卦象显示当前时机较为平稳，宜静不宜动。上卦${hexagram.upper.name}代表天时，下卦${hexagram.lower.name}代表地利，两卦相配，暗示需要耐心等待时机。

建议：
1. 近期宜保守行事，不宜冒进
2. 注意人际关系的维护
3. 财运方面需要谨慎理财
4. 感情方面宜真诚相待

起卦时间：${hexagram.time}`;

      this.setData({
        analysis: analysis,
        isAnalyzing: false
      });

      // 扣除积分
      app.globalData.credits -= 1;
      
      wx.showToast({
        title: '分析完成',
        icon: 'success'
      });
    }, 2000);
  },

  // 重新起卦
  onRestart() {
    this.setData({
      hexagram: null,
      analysis: '',
      isAnalyzing: false
    });
  }
});
