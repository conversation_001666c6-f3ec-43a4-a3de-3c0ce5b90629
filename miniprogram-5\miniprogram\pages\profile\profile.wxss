/* pages/profile/profile.wxss - 个人中心页面样式 */

page {
  background: var(--paper-white);
  padding-bottom: 120rpx;
}

.profile-container {
  min-height: 100vh;
}

/* 用户信息头部 */
.user-header {
  background: white;
  padding: 60rpx 30rpx 40rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.06);
  margin-bottom: 40rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  overflow: hidden;
  background: var(--ink-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.guest-avatar {
  background: linear-gradient(135deg, var(--ink-gray) 0%, var(--ink-light) 100%);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guest-icon {
  color: white;
  font-size: 40rpx;
  font-weight: 500;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 12rpx;
}

.user-credits {
  display: flex;
  align-items: center;
}

.credits-label {
  font-size: 28rpx;
  color: var(--ink-gray);
}

.credits-value {
  font-size: 32rpx;
  color: var(--ancient-gold);
  font-weight: 600;
  margin-left: 8rpx;
}

.login-tip {
  margin-top: 8rpx;
}

.tip-text {
  font-size: 24rpx;
  color: var(--ink-light);
}

.login-button {
  background: linear-gradient(135deg, var(--ink-black) 0%, #333333 100%);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.15);
}

.login-text {
  color: white;
}

/* 功能菜单 */
.menu-section {
  background: white;
  border-radius: 20rpx;
  margin: 0 30rpx 40rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.06);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f8f8;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: var(--ancient-paper);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 24rpx;
}

.menu-title {
  flex: 1;
  font-size: 30rpx;
  color: var(--ink-black);
  font-weight: 500;
}

.menu-arrow {
  font-size: 28rpx;
  color: var(--ink-light);
  font-weight: 600;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 60rpx 30rpx;
}

.version-text {
  font-size: 28rpx;
  color: var(--ink-gray);
  margin-bottom: 12rpx;
  font-weight: 500;
}

.copyright-text {
  font-size: 24rpx;
  color: var(--ink-light);
  line-height: 1.5;
}