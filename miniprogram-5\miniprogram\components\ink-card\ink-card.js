// components/ink-card/ink-card.js - 水墨风卡片组件
Component({
  properties: {
    // 卡片标题
    title: {
      type: String,
      value: ''
    },
    // 卡片副标题
    subtitle: {
      type: String,
      value: ''
    },
    // 是否显示阴影
    shadow: {
      type: Boolean,
      value: true
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      value: false
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    // 卡片类型：default, bordered, elevated
    type: {
      type: String,
      value: 'default'
    }
  },

  data: {
    pressed: false
  },

  methods: {
    // 卡片点击事件
    onTap(e) {
      if (!this.data.clickable) return;
      
      this.triggerEvent('tap', e.detail);
    },

    // 卡片按下
    onTouchStart() {
      if (!this.data.clickable) return;
      this.setData({ pressed: true });
    },

    // 卡片松开
    onTouchEnd() {
      this.setData({ pressed: false });
    },

    // 取消按下
    onTouchCancel() {
      this.setData({ pressed: false });
    }
  }
});
