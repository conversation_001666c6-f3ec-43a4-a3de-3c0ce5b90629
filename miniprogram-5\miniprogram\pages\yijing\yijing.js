// pages/yijing/yijing.js - 周易卦象（六爻）页面
const app = getApp();

Page({
  data: {
    question: '', // 要问的事
    hexagram: null, // 卦象结果
    analysis: '', // 分析结果
    isAnalyzing: false, // 是否正在分析
    coinResults: [], // 投币结果
    isThrowingCoins: false, // 是否正在投币
    currentThrow: 0, // 当前投币次数
    totalThrows: 6 // 总投币次数
  },

  onLoad() {
    console.log('周易卦象页面加载');
  },

  // 输入问题
  onQuestionInput(e) {
    this.setData({
      question: e.detail.value
    });
  },

  // 开始六爻起卦
  onStartDivination() {
    if (this.data.isThrowingCoins || this.data.isAnalyzing) return;

    // 检查是否输入问题
    if (!this.data.question.trim()) {
      wx.showToast({
        title: '请输入要问的事',
        icon: 'none'
      });
      return;
    }

    // 检查积分
    if (app.globalData.credits < 2) {
      wx.showModal({
        title: '积分不足',
        content: '六爻占卜需要2积分，请先充值',
        confirmText: '去充值',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/recharge/recharge'
            });
          }
        }
      });
      return;
    }

    // 重置状态
    this.setData({
      coinResults: [],
      hexagram: null,
      analysis: '',
      isThrowingCoins: true,
      currentThrow: 0
    });

    // 开始投币
    this.throwCoins();
  },

  // 投币起卦（模拟三枚铜钱法）
  throwCoins() {
    if (this.data.currentThrow >= this.data.totalThrows) {
      // 投币完成，生成卦象
      this.generateHexagram();
      return;
    }

    // 模拟投币过程
    setTimeout(() => {
      // 三枚铜钱，每枚有正反两面
      // 正面为阳（3），反面为阴（2）
      // 三枚铜钱的组合：
      // 三个正面 = 9 (老阳，动爻)
      // 两正一反 = 8 (少阴，静爻)
      // 一正两反 = 7 (少阳，静爻)
      // 三个反面 = 6 (老阴，动爻)

      const coin1 = Math.random() > 0.5 ? 3 : 2; // 正面3，反面2
      const coin2 = Math.random() > 0.5 ? 3 : 2;
      const coin3 = Math.random() > 0.5 ? 3 : 2;
      const total = coin1 + coin2 + coin3;

      let yaoType, yaoSymbol, isChanging;

      switch(total) {
        case 9: // 老阳，动爻
          yaoType = '老阳';
          yaoSymbol = '━━━';
          isChanging = true;
          break;
        case 8: // 少阴，静爻
          yaoType = '少阴';
          yaoSymbol = '━ ━';
          isChanging = false;
          break;
        case 7: // 少阳，静爻
          yaoType = '少阳';
          yaoSymbol = '━━━';
          isChanging = false;
          break;
        case 6: // 老阴，动爻
          yaoType = '老阴';
          yaoSymbol = '━ ━';
          isChanging = true;
          break;
      }

      const newResults = [...this.data.coinResults];
      newResults.push({
        throw: this.data.currentThrow + 1,
        coins: [coin1, coin2, coin3],
        total: total,
        yaoType: yaoType,
        yaoSymbol: yaoSymbol,
        isChanging: isChanging
      });

      this.setData({
        coinResults: newResults,
        currentThrow: this.data.currentThrow + 1
      });

      // 继续下一次投币
      setTimeout(() => {
        this.throwCoins();
      }, 1000);
    }, 800);
  },

  // 生成卦象
  generateHexagram() {
    this.setData({
      isThrowingCoins: false,
      isAnalyzing: true
    });

    // 根据六爻结果生成本卦和变卦
    const yaos = this.data.coinResults;

    // 生成本卦（从下往上）
    const originalLines = yaos.map(yao => {
      return yao.yaoType === '老阳' || yao.yaoType === '少阳' ? 1 : 0; // 1为阳，0为阴
    });

    // 生成变卦
    const changedLines = yaos.map(yao => {
      if (yao.isChanging) {
        // 动爻变化：老阳变少阴，老阴变少阳
        return yao.yaoType === '老阳' ? 0 : 1;
      } else {
        // 静爻不变
        return yao.yaoType === '少阳' ? 1 : 0;
      }
    });

    // 获取动爻位置
    const changingYaos = yaos.map((yao, index) => yao.isChanging ? index + 1 : null).filter(x => x !== null);

    const hexagram = {
      question: this.data.question,
      originalLines: originalLines,
      changedLines: changedLines,
      changingYaos: changingYaos,
      yaos: yaos,
      time: new Date().toLocaleString('zh-CN')
    };

    setTimeout(() => {
      this.setData({
        hexagram: hexagram
      });

      // AI分析
      this.analyzeHexagram(hexagram);
    }, 1500);
  },

  // 分析卦象
  analyzeHexagram(hexagram) {
    // 这里应该调用DeepSeek API进行分析
    // 现在先用模拟数据
    setTimeout(() => {
      const changingText = hexagram.changingYaos.length > 0 ?
        `动爻：第${hexagram.changingYaos.join('、')}爻` : '无动爻';

      const analysis = `【周易六爻占卜结果】

所问之事：${hexagram.question}
起卦时间：${hexagram.time}
起卦方法：三枚铜钱法

卦象分析：
${changingText}

六爻详解：
${hexagram.yaos.map((yao, index) =>
  `第${index + 1}爻：${yao.yaoSymbol} (${yao.yaoType}${yao.isChanging ? '，动' : ''})`
).reverse().join('\n')}

卦象解读：
根据传统六爻理论，此卦象显示：

本卦反映当前状况和问题的本质
${hexagram.changingYaos.length > 0 ? '变卦显示事情的发展趋势和最终结果' : '卦象稳定，无明显变化'}

针对您所问"${hexagram.question}"：
建议您根据卦象指示，顺应自然规律，把握时机，做出明智的决策。

占卜建议：
1. 仔细观察当前形势
2. 注意动爻所示的变化
3. 结合实际情况判断
4. 保持内心的平静和智慧

注：此为传统六爻占法，仅供参考，最终决策请结合实际情况。`;

      this.setData({
        analysis: analysis,
        isAnalyzing: false
      });

      // 扣除积分
      app.globalData.credits -= 2;

      wx.showToast({
        title: '占卜完成',
        icon: 'success'
      });
    }, 2000);
  },

  // 重新起卦
  onRestart() {
    this.setData({
      hexagram: null,
      analysis: '',
      coinResults: [],
      isAnalyzing: false,
      isThrowingCoins: false,
      currentThrow: 0
    });
  }
});