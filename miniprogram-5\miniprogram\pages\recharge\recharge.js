// pages/recharge/recharge.js - 充值页面
const app = getApp();

Page({
  data: {
    currentCredits: 0,
    rechargeOptions: [
      {
        id: 1,
        credits: 10,
        price: 9.9,
        originalPrice: 10,
        discount: '限时优惠',
        popular: false
      },
      {
        id: 2,
        credits: 30,
        price: 29.9,
        originalPrice: 30,
        discount: '推荐',
        popular: true
      },
      {
        id: 3,
        credits: 50,
        price: 49.9,
        originalPrice: 50,
        discount: '超值',
        popular: false
      },
      {
        id: 4,
        credits: 100,
        price: 99.9,
        originalPrice: 100,
        discount: '最划算',
        popular: false
      }
    ],
    selectedOption: null
  },

  onLoad() {
    console.log('充值页面加载');
    this.setData({
      currentCredits: app.globalData.credits || 0,
      selectedOption: this.data.rechargeOptions[1] // 默认选择推荐套餐
    });
  },

  onShow() {
    // 刷新当前积分
    this.setData({
      currentCredits: app.globalData.credits || 0
    });
  },

  // 选择充值套餐
  onSelectOption(e) {
    const { option } = e.currentTarget.dataset;
    this.setData({
      selectedOption: option
    });
  },

  // 立即充值
  onRecharge() {
    if (!this.data.selectedOption) {
      wx.showToast({
        title: '请选择充值套餐',
        icon: 'none'
      });
      return;
    }

    const option = this.data.selectedOption;
    
    wx.showModal({
      title: '确认充值',
      content: `确认充值${option.credits}积分，支付${option.price}元？`,
      confirmText: '确认支付',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.processPayment(option);
        }
      }
    });
  },

  // 处理支付
  processPayment(option) {
    wx.showLoading({
      title: '正在支付...'
    });

    // 模拟支付过程
    setTimeout(() => {
      wx.hideLoading();
      
      // 模拟支付成功
      const success = Math.random() > 0.1; // 90%成功率
      
      if (success) {
        // 更新积分
        app.globalData.credits = (app.globalData.credits || 0) + option.credits;
        
        this.setData({
          currentCredits: app.globalData.credits
        });

        wx.showModal({
          title: '充值成功',
          content: `恭喜您成功充值${option.credits}积分！当前积分：${app.globalData.credits}`,
          showCancel: false,
          confirmText: '确定',
          success: () => {
            // 可以选择返回上一页或留在充值页面
            // wx.navigateBack();
          }
        });

        // 记录充值历史（这里应该调用云函数保存到数据库）
        this.recordRechargeHistory(option);
        
      } else {
        wx.showModal({
          title: '支付失败',
          content: '支付过程中出现问题，请重试',
          showCancel: false
        });
      }
    }, 2000);
  },

  // 记录充值历史
  recordRechargeHistory(option) {
    // 这里应该调用云函数保存充值记录
    console.log('充值记录：', {
      credits: option.credits,
      price: option.price,
      time: new Date(),
      userId: app.globalData.userInfo?.openId
    });
  },

  // 查看充值记录
  onViewHistory() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 联系客服
  onContactService() {
    wx.showModal({
      title: '联系客服',
      content: '如有充值问题，请添加客服微信：yuanhenglizheng',
      confirmText: '复制微信号',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: 'yuanhenglizheng',
            success: () => {
              wx.showToast({
                title: '微信号已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  }
});
