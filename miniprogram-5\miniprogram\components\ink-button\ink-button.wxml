<!--components/ink-button/ink-button.wxml - 水墨风按钮模板-->
<view 
  class="ink-button {{type}} {{size}} {{customClass}} {{disabled ? 'disabled' : ''}} {{loading ? 'loading' : ''}} {{pressed ? 'pressed' : ''}} {{round ? 'round' : ''}}"
  bindtap="onTap"
  bindtouchstart="onTouchStart"
  bindtouchend="onTouchEnd"
  bindtouchcancel="onTouchCancel"
>
  <!-- 水墨渐变背景 -->
  <view class="ink-bg"></view>
  
  <!-- 按钮内容 -->
  <view class="button-content">
    <!-- 加载动画 -->
    <view class="loading-icon" wx:if="{{loading}}">
      <view class="ink-spinner">
        <view class="spinner-dot"></view>
        <view class="spinner-dot"></view>
        <view class="spinner-dot"></view>
      </view>
    </view>
    
    <!-- 按钮文字 -->
    <text class="button-text" wx:if="{{!loading}}">{{text}}</text>
  </view>
  
  <!-- 水墨涟漪效果 -->
  <view class="ink-ripple" wx:if="{{pressed}}"></view>
</view>
