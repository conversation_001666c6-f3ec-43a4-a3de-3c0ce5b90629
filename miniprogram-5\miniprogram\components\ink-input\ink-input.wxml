<!--components/ink-input/ink-input.wxml - 水墨风输入框模板-->
<view class="ink-input-container {{customClass}}">
  <!-- 标签 -->
  <view class="input-label" wx:if="{{label}}">
    <text class="label-text">{{label}}</text>
    <text class="required-mark" wx:if="{{required}}">*</text>
  </view>
  
  <!-- 输入框容器 -->
  <view class="input-wrapper {{focused ? 'focused' : ''}} {{disabled ? 'disabled' : ''}} {{error ? 'error' : ''}}">
    <!-- 水墨装饰线 -->
    <view class="ink-decoration"></view>
    
    <!-- 输入框 -->
    <input
      class="input-field"
      type="{{type}}"
      value="{{currentValue}}"
      placeholder="{{placeholder}}"
      disabled="{{disabled}}"
      maxlength="{{maxlength}}"
      bindinput="onInput"
      bindfocus="onFocus"
      bindblur="onBlur"
      bindconfirm="onConfirm"
    />
    
    <!-- 清空按钮 -->
    <view class="clear-btn" wx:if="{{currentValue && !disabled}}" bindtap="onClear">
      <text class="clear-icon">×</text>
    </view>
    
    <!-- 水墨边框 -->
    <view class="ink-border"></view>
  </view>
  
  <!-- 错误信息 -->
  <view class="error-message" wx:if="{{error}}">
    <text class="error-text">{{error}}</text>
  </view>
  
  <!-- 字数统计 -->
  <view class="char-count" wx:if="{{maxlength && focused}}">
    <text class="count-text">{{currentValue.length}}/{{maxlength}}</text>
  </view>
</view>
