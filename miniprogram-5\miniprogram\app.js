// app.js - 元亨利贞小程序主入口
App({
  onLaunch: function () {
    console.log('元亨利贞小程序启动');

    // 初始化云开发
    this.initCloud();

    // 检查用户登录状态
    this.checkUserLogin();
  },

  // 初始化云开发
  initCloud() {
    if (!wx.cloud) {
      console.error("请使用 2.2.3 或以上的基础库以使用云能力");
      wx.showToast({
        title: '需要更新微信版本',
        icon: 'none'
      });
      return;
    }

    try {
      wx.cloud.init({
        env: this.globalData.env,
        traceUser: true,
      });
      console.log('云开发初始化成功，环境:', this.globalData.env);
    } catch (error) {
      console.error('云开发初始化失败:', error);
      // 尝试使用空环境ID（自动选择）
      try {
        wx.cloud.init({
          traceUser: true,
        });
        console.log('云开发初始化成功（自动环境）');
      } catch (error2) {
        console.error('云开发完全初始化失败:', error2);
        // 云开发初始化失败不影响小程序基本功能
      }
    }
  },



  // 检查用户登录状态
  checkUserLogin() {
    const userInfo = wx.getStorageSync('userInfo');
    const openid = wx.getStorageSync('openid');

    if (userInfo && openid) {
      this.globalData.userInfo = userInfo;
      this.globalData.openid = openid;
      console.log('用户已登录:', userInfo.nickName);
    } else {
      console.log('用户未登录，需要授权');
    }
  },

  // 获取用户信息
  getUserInfo() {
    return new Promise((resolve, reject) => {
      if (this.globalData.userInfo) {
        resolve(this.globalData.userInfo);
        return;
      }

      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          this.globalData.userInfo = res.userInfo;
          wx.setStorageSync('userInfo', res.userInfo);
          resolve(res.userInfo);
        },
        fail: reject
      });
    });
  },

  // 获取OpenID（模拟版本，避免云函数依赖）
  getOpenId() {
    return new Promise((resolve, reject) => {
      if (this.globalData.openid) {
        resolve(this.globalData.openid);
        return;
      }

      // 尝试使用云函数获取真实OpenID
      if (wx.cloud && wx.cloud.callFunction) {
        wx.cloud.callFunction({
          name: 'getOpenId',
          success: (res) => {
            const openid = res.result.openid;
            this.globalData.openid = openid;
            wx.setStorageSync('openid', openid);
            resolve(openid);
          },
          fail: (error) => {
            console.log('云函数获取OpenID失败，使用模拟OpenID:', error);
            // 生成模拟OpenID
            const mockOpenId = 'mock_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            this.globalData.openid = mockOpenId;
            wx.setStorageSync('openid', mockOpenId);
            resolve(mockOpenId);
          }
        });
      } else {
        // 云开发未初始化，直接使用模拟OpenID
        const mockOpenId = 'mock_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        this.globalData.openid = mockOpenId;
        wx.setStorageSync('openid', mockOpenId);
        resolve(mockOpenId);
      }
    });
  },

  // 显示加载提示
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    });
  },

  // 隐藏加载提示
  hideLoading() {
    wx.hideLoading();
  },

  // 显示成功提示
  showSuccess(title) {
    wx.showToast({
      title: title,
      icon: 'success',
      duration: 2000
    });
  },

  // 显示错误提示
  showError(title) {
    wx.showToast({
      title: title,
      icon: 'none',
      duration: 3000
    });
  },

  globalData: {
    // 云开发环境ID - 使用默认环境
    env: "default",

    // 用户信息
    userInfo: null,
    openid: null,
    phone: null,
    credits: 0,

    // DeepSeek API配置
    deepseekApiKey: "***********************************",
    deepseekApiUrl: "https://api.deepseek.com/v1/chat/completions",

    // 应用配置
    appName: "元亨利贞",
    version: "1.0.0",

    // 积分配置
    registerCredits: 10,    // 注册赠送积分
    inviteCredits: 50,      // 邀请好友积分

    // 功能价格配置
    prices: {
      yijing: 3,
      bazi: 2,
      ziwei: 3,
      meihua: 1
    }
  }
});
