/* components/ink-input/ink-input.wxss - 水墨风输入框样式 */

.ink-input-container {
  margin-bottom: 32rpx;
  font-family: 'STSong', '华文宋体', serif;
}

/* 标签样式 */
.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  color: var(--ink-black);
  font-size: 28rpx;
  font-weight: 500;
}

.required-mark {
  color: #d32f2f;
  font-size: 28rpx;
  margin-left: 4rpx;
}

/* 输入框容器 */
.input-wrapper {
  position: relative;
  background: var(--paper-white);
  border-radius: 12rpx;
  transition: all 0.3s ease;
  overflow: hidden;
}

/* 水墨装饰线 */
.ink-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--ink-light) 20%, 
    var(--ink-gray) 50%, 
    var(--ink-light) 80%, 
    transparent 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

/* 输入框 */
.input-field {
  width: 100%;
  height: 88rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  color: var(--ink-black);
  background: transparent;
  border: none;
  outline: none;
  box-sizing: border-box;
}

.input-field::placeholder {
  color: var(--ink-light);
  font-size: 26rpx;
}

/* 清空按钮 */
.clear-btn {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--ink-light);
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-icon {
  color: white;
  font-size: 32rpx;
  line-height: 1;
}

.clear-btn:active {
  transform: translateY(-50%) scale(0.9);
  background: var(--ink-gray);
}

/* 水墨边框 */
.ink-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2rpx solid var(--ink-light);
  border-radius: inherit;
  transition: all 0.3s ease;
  pointer-events: none;
}

/* 聚焦状态 */
.input-wrapper.focused .ink-decoration {
  transform: scaleX(1);
}

.input-wrapper.focused .ink-border {
  border-color: var(--ancient-gold);
  box-shadow: 0 0 0 4rpx rgba(212, 175, 55, 0.1);
}

/* 禁用状态 */
.input-wrapper.disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.input-wrapper.disabled .input-field {
  color: var(--ink-light);
  cursor: not-allowed;
}

.input-wrapper.disabled .ink-border {
  border-color: #e0e0e0;
}

/* 错误状态 */
.input-wrapper.error .ink-border {
  border-color: #d32f2f;
}

.input-wrapper.error.focused .ink-border {
  box-shadow: 0 0 0 4rpx rgba(211, 47, 47, 0.1);
}

/* 错误信息 */
.error-message {
  margin-top: 8rpx;
}

.error-text {
  color: #d32f2f;
  font-size: 24rpx;
  line-height: 1.4;
}

/* 字数统计 */
.char-count {
  margin-top: 8rpx;
  text-align: right;
}

.count-text {
  color: var(--ink-light);
  font-size: 22rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .input-field {
    height: 80rpx;
    padding: 0 24rpx;
    font-size: 26rpx;
  }
  
  .label-text {
    font-size: 26rpx;
  }
}
