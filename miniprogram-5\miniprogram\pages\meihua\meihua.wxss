/* pages/meihua/meihua.wxss - 梅花易数页面样式 */

page {
  background: var(--paper-white);
  padding-bottom: 120rpx;
}

.meihua-container {
  min-height: 100vh;
  padding: 40rpx 30rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.header-title {
  font-size: 48rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 16rpx;
  font-family: 'STKaiti', '楷体', serif;
}

.header-subtitle {
  font-size: 28rpx;
  color: var(--ink-gray);
  font-weight: 400;
}

/* 时间显示区域 */
.time-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
  text-align: center;
}

.time-label {
  font-size: 28rpx;
  color: var(--ink-gray);
  margin-bottom: 20rpx;
}

.time-display {
  font-size: 36rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 20rpx;
  font-family: 'Courier New', monospace;
}

.time-tip {
  font-size: 24rpx;
  color: var(--ink-light);
  line-height: 1.5;
}

/* 起卦按钮 */
.action-section {
  margin-bottom: 40rpx;
}

.start-button {
  background: linear-gradient(135deg, var(--ink-black) 0%, #333333 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 12rpx 40rpx rgba(26, 26, 26, 0.15);
}

.button-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.button-text {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.button-cost {
  color: var(--ancient-gold);
  font-size: 24rpx;
  font-weight: 500;
}

/* 分析中动画 */
.analyzing-section {
  text-align: center;
  padding: 80rpx 40rpx;
}

.analyzing-animation {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
}

.ink-drop {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: var(--ink-black);
  margin: 0 8rpx;
  animation: inkPulse 1.5s ease-in-out infinite;
}

.ink-drop:nth-child(2) {
  animation-delay: 0.3s;
}

.ink-drop:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes inkPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
}

.analyzing-text {
  font-size: 28rpx;
  color: var(--ink-gray);
}

/* 卦象显示 */
.hexagram-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
}

.hexagram-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.hexagram-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 12rpx;
}

.hexagram-time {
  font-size: 24rpx;
  color: var(--ink-gray);
}

.hexagram-display {
  text-align: center;
}

.trigram-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.trigram {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  border-radius: 16rpx;
  background: var(--ancient-paper);
  min-width: 200rpx;
}

.upper-trigram {
  border: 2rpx solid var(--ink-black);
}

.lower-trigram {
  border: 2rpx solid var(--ink-gray);
}

.trigram-symbol {
  font-size: 60rpx;
  color: var(--ink-black);
  margin-bottom: 16rpx;
  font-family: 'STKaiti', '楷体', serif;
}

.trigram-name {
  font-size: 28rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 8rpx;
}

.trigram-label {
  font-size: 22rpx;
  color: var(--ink-gray);
}

.hexagram-center {
  padding: 20rpx;
}

.change-line {
  font-size: 24rpx;
  color: var(--ancient-gold);
  font-weight: 600;
  background: rgba(212, 175, 55, 0.1);
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  display: inline-block;
}

/* 分析结果 */
.analysis-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
}

.analysis-header {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.analysis-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  text-align: center;
}

.analysis-content {
  line-height: 1.8;
}

.analysis-text {
  font-size: 28rpx;
  color: var(--ink-black);
  white-space: pre-line;
}

/* 底部操作按钮 */
.bottom-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.action-button {
  flex: 1;
  padding: 24rpx;
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.1);
}

.secondary-button {
  background: white;
  border: 2rpx solid var(--ink-gray);
}

.action-text {
  font-size: 28rpx;
  color: var(--ink-gray);
  font-weight: 500;
}
