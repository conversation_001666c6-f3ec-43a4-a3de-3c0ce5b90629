/**app.wxss - 元亨利贞水墨风全局样式**/

/* 全局变量定义 */
page {
  --ink-black: #1a1a1a;
  --ink-gray: #666666;
  --ink-light: #999999;
  --paper-white: #fafafa;
  --ancient-paper: #f8f8f0;
  --ancient-gold: #d4af37;

  background: var(--paper-white);
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  color: var(--ink-black);
  line-height: 1.6;
}

/* 容器基础样式 */
.container {
  min-height: 100vh;
  box-sizing: border-box;
  background: var(--paper-white);
}

/* 按钮重置 */
button {
  background: initial;
  border: none;
  outline: none;
  font-family: inherit;
}

button:focus {
  outline: 0;
}

button::after {
  border: none;
}

/* 水墨风基础组件样式 */
.ink-title {
  font-family: 'STSong', '华文宋体', serif;
  font-size: 48rpx;
  font-weight: 500;
  color: var(--ink-black);
  text-align: center;
  margin: 40rpx 0;
  position: relative;
}

.ink-subtitle {
  font-size: 32rpx;
  color: var(--ink-gray);
  text-align: center;
  margin: 20rpx 0;
}

.ink-text {
  font-size: 28rpx;
  color: var(--ink-black);
  line-height: 1.8;
}

.ink-tip {
  font-size: 24rpx;
  color: var(--ink-light);
  line-height: 1.6;
}

/* 水墨渐变背景 */
.ink-gradient-bg {
  background: linear-gradient(135deg,
    var(--paper-white) 0%,
    var(--ancient-paper) 50%,
    var(--paper-white) 100%);
}

/* 水墨阴影效果 */
.ink-shadow {
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.08);
}

/* 水墨边框 */
.ink-border {
  border: 2rpx solid rgba(26, 26, 26, 0.1);
  border-radius: 16rpx;
}

/* 古典分割线 */
.ink-divider {
  height: 2rpx;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--ink-light) 50%,
    transparent 100%);
  margin: 40rpx 0;
}

/* 水墨动画基础类 */
.ink-fade-in {
  animation: inkFadeIn 0.8s ease-out;
}

@keyframes inkFadeIn {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 墨迹扩散动画 */
.ink-ripple {
  position: relative;
  overflow: hidden;
}

.ink-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(26, 26, 26, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ink-ripple:active::before {
  width: 200%;
  height: 200%;
}

/* 响应式布局 */
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 20rpx; }
.m-2 { margin: 40rpx; }
.m-3 { margin: 60rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 20rpx; }
.p-2 { padding: 40rpx; }
.p-3 { padding: 60rpx; }