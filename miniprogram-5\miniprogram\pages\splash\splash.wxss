/* pages/splash/splash.wxss - 启动页面样式 */

.splash-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 水墨渐变背景 */
.ink-background {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    #fafafa 0%, 
    #f8f8f0 30%, 
    #fafafa 60%, 
    #f5f5f5 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 水墨纹理层 */
.ink-texture {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 30%, rgba(26, 26, 26, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(26, 26, 26, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(26, 26, 26, 0.01) 0%, transparent 50%);
  animation: inkFlow 8s ease-in-out infinite;
}

@keyframes inkFlow {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

/* 主要内容区域 */
.content-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  padding: 100rpx 60rpx;
}

/* Logo区域 */
.logo-section {
  margin-bottom: 80rpx;
  position: relative;
}

.main-logo {
  position: relative;
  z-index: 2;
}

.logo-char {
  font-size: 200rpx;
  font-weight: 900;
  color: var(--ink-black);
  font-family: 'STKaiti', '楷体', serif;
  text-shadow: 0 0 20rpx rgba(26, 26, 26, 0.3);
  display: block;
  text-align: center;
}

/* 水墨飞溅效果 */
.ink-splash {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, 
    rgba(26, 26, 26, 0.1) 0%, 
    rgba(26, 26, 26, 0.05) 30%, 
    transparent 70%);
  border-radius: 50%;
  animation: inkSplash 3s ease-out infinite;
  z-index: 1;
}

@keyframes inkSplash {
  0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.6; }
  100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
}

/* 标题区域 */
.title-section {
  margin-bottom: 60rpx;
  text-align: center;
  position: relative;
}

.app-title {
  position: relative;
}

.title-text {
  font-size: 72rpx;
  font-weight: 500;
  color: var(--ink-black);
  font-family: 'STSong', '华文宋体', serif;
  letter-spacing: 8rpx;
  display: block;
}

.title-decoration {
  width: 200rpx;
  height: 4rpx;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--ink-gray) 50%, 
    transparent 100%);
  margin: 20rpx auto;
  border-radius: 2rpx;
}

/* 副标题区域 */
.subtitle-section {
  margin-bottom: 120rpx;
  text-align: center;
}

.subtitle-text {
  font-size: 32rpx;
  color: var(--ink-gray);
  font-weight: 300;
  letter-spacing: 2rpx;
}

.subtitle-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;
}

.decoration-line {
  width: 60rpx;
  height: 2rpx;
  background: var(--ink-light);
}

.decoration-dot {
  width: 8rpx;
  height: 8rpx;
  background: var(--ink-light);
  border-radius: 50%;
  margin: 0 20rpx;
}

/* 登录按钮区域 */
.login-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40rpx;
}

.login-button {
  width: 500rpx;
  height: 88rpx;
  background: linear-gradient(135deg, 
    var(--ink-black) 0%, 
    #333333 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(26, 26, 26, 0.2);
  position: relative;
  overflow: hidden;
}

.button-text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 2rpx;
}

.skip-button {
  padding: 20rpx 40rpx;
}

.skip-text {
  color: var(--ink-light);
  font-size: 28rpx;
  text-decoration: underline;
}

/* 底部装饰 */
.bottom-decoration {
  position: absolute;
  bottom: 80rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.ink-drops {
  display: flex;
  gap: 20rpx;
}

.drop {
  width: 12rpx;
  height: 12rpx;
  background: var(--ink-light);
  border-radius: 50%;
  animation: dropFall 2s ease-in-out infinite;
}

.drop-1 { animation-delay: 0s; }
.drop-2 { animation-delay: 0.3s; }
.drop-3 { animation-delay: 0.6s; }

@keyframes dropFall {
  0%, 100% { transform: translateY(0); opacity: 0.3; }
  50% { transform: translateY(20rpx); opacity: 0.8; }
}

.ancient-pattern {
  text-align: center;
}

.pattern-text {
  font-size: 24rpx;
  color: var(--ink-light);
  font-style: italic;
  letter-spacing: 1rpx;
}

/* 水墨渐现动画增强 */
.ink-fade-in {
  animation: inkFadeIn 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes inkFadeIn {
  0% {
    opacity: 0;
    transform: translateY(60rpx) scale(0.9);
    filter: blur(10rpx);
  }
  60% {
    opacity: 0.8;
    transform: translateY(10rpx) scale(1.02);
    filter: blur(2rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}
