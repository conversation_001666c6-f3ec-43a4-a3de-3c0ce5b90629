<!--pages/splash/splash.wxml - 启动页面模板-->
<view class="splash-container">
  <!-- 水墨渐变背景 -->
  <view class="ink-background">
    <!-- 水墨纹理层 -->
    <view class="ink-texture"></view>
    
    <!-- 主要内容区域 -->
    <view class="content-area">
      <!-- 水墨Logo区域 -->
      <view class="logo-section" wx:if="{{showLogo}}">
        <view class="main-logo ink-fade-in">
          <text class="logo-char">元</text>
        </view>
        <view class="ink-splash"></view>
      </view>
      
      <!-- 标题区域 -->
      <view class="title-section" wx:if="{{showTitle}}">
        <view class="app-title ink-fade-in">
          <text class="title-text">元亨利贞</text>
        </view>
        <view class="title-decoration"></view>
      </view>
      
      <!-- 副标题区域 -->
      <view class="subtitle-section" wx:if="{{showSubtitle}}">
        <view class="subtitle ink-fade-in">
          <text class="subtitle-text">千年古籍 • AI智慧</text>
        </view>
        <view class="subtitle-decoration">
          <view class="decoration-line"></view>
          <view class="decoration-dot"></view>
          <view class="decoration-line"></view>
        </view>
      </view>
      
      <!-- 登录按钮区域 -->
      <view class="login-section" wx:if="{{showButton}}">
        <view class="login-button ink-fade-in ink-ripple" bindtap="onWechatLogin">
          <text class="button-text">微信授权登录</text>
        </view>
        
        <!-- 跳过按钮 -->
        <view class="skip-button" bindtap="onSkipLogin">
          <text class="skip-text">暂不登录，先体验</text>
        </view>
      </view>
    </view>
    
    <!-- 底部装饰 -->
    <view class="bottom-decoration">
      <view class="ink-drops">
        <view class="drop drop-1"></view>
        <view class="drop drop-2"></view>
        <view class="drop drop-3"></view>
      </view>
      <view class="ancient-pattern">
        <text class="pattern-text">～～～ 墨迹飞白效果 ～～～</text>
      </view>
    </view>
  </view>
</view>
